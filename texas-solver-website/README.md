# 德州Solver官网

一个采用苹果风格设计的专业德州扑克GTO分析工具官网。

## 🎯 项目特点

### 设计风格
- **苹果风格设计语言**: 简洁、现代、优雅的视觉设计
- **响应式布局**: 完美适配桌面、平板和移动设备
- **流畅动画**: 精心设计的交互动画和过渡效果
- **高质量视觉**: 专业级的UI/UX设计

### 技术特性
- **纯前端实现**: HTML5 + CSS3 + JavaScript
- **现代CSS**: 使用CSS Grid、Flexbox、CSS变量等现代特性
- **优化性能**: 轻量级代码，快速加载
- **SEO友好**: 语义化HTML结构，优化搜索引擎收录

### 功能模块
- **英雄区域**: 产品介绍和核心价值展示
- **功能展示**: 六大核心功能详细介绍
- **产品演示**: 交互式三步演示流程
- **技术优势**: 四大技术特点展示
- **用户评价**: 真实用户反馈展示
- **定价方案**: 三种定价套餐对比
- **下载区域**: 多平台下载链接
- **完整页脚**: 详细的导航和联系信息

## 🚀 快速开始

### 本地运行
1. 克隆或下载项目文件
2. 使用任何HTTP服务器运行（推荐使用Live Server）
3. 在浏览器中访问 `index.html`

### 推荐工具
- **VS Code** + Live Server 扩展
- **Python**: `python -m http.server 8000`
- **Node.js**: `npx serve .`

## 📁 项目结构

```
texas-solver-website/
├── index.html          # 主页面
├── css/
│   └── style.css       # 样式文件
├── js/
│   └── main.js         # JavaScript功能
└── README.md           # 项目说明
```

## 🎨 设计系统

### 颜色方案
- **主色调**: #007AFF (苹果蓝)
- **次要色**: #5856D6 (紫色)
- **强调色**: #FF3B30 (红色)
- **成功色**: #34C759 (绿色)
- **警告色**: #FF9500 (橙色)

### 字体系统
- **主字体**: SF Pro Display (苹果系统字体)
- **备用字体**: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto

### 间距系统
- 基于 0.25rem (4px) 的倍数系统
- 从 xs (0.25rem) 到 4xl (6rem) 的完整间距体系

## ✨ 核心功能

### 1. 响应式导航
- 固定顶部导航栏
- 滚动时背景模糊效果
- 移动端汉堡菜单
- 平滑滚动到对应区域

### 2. 英雄区域
- 渐变文字效果
- 3D扑克界面展示
- 浮动扑克牌动画
- 统计数据计数动画

### 3. 功能展示
- 六宫格布局
- 悬停动画效果
- 图标和描述完美结合
- 渐进式加载动画

### 4. 交互演示
- 三步式演示流程
- 点击切换演示内容
- 自动轮播功能
- 实时预览效果

### 5. 定价方案
- 三种套餐对比
- 推荐标签突出显示
- 功能列表清晰展示
- 悬停放大效果

## 🔧 自定义配置

### CSS变量
所有颜色、字体、间距都通过CSS变量定义，便于主题定制：

```css
:root {
    --primary-color: #007AFF;
    --font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont;
    --spacing-md: 1rem;
    /* 更多变量... */
}
```

### 动画配置
可以通过修改CSS变量调整动画时长：

```css
:root {
    --transition-fast: 0.15s ease-out;
    --transition-normal: 0.3s ease-out;
    --transition-slow: 0.5s ease-out;
}
```

## 📱 响应式设计

### 断点设置
- **桌面**: > 1024px
- **平板**: 768px - 1024px  
- **手机**: < 768px

### 适配特性
- 导航栏在移动端转为汉堡菜单
- 网格布局自动调整列数
- 文字大小和间距响应式缩放
- 图片和视频自适应容器

## 🎯 SEO优化

### 元数据
- 完整的meta标签设置
- 结构化数据标记
- 语义化HTML标签
- 图片alt属性优化

### 性能优化
- CSS和JS文件压缩
- 图片懒加载
- 关键CSS内联
- 字体预加载

## 🌟 浏览器兼容性

### 支持的浏览器
- **Chrome**: 60+
- **Firefox**: 60+
- **Safari**: 12+
- **Edge**: 79+

### 渐进增强
- 基础功能在所有现代浏览器中可用
- 高级动画效果在支持的浏览器中启用
- 优雅降级处理

## 🚀 部署建议

### 静态托管
- **Netlify**: 推荐，支持自动部署
- **Vercel**: 优秀的性能和CDN
- **GitHub Pages**: 免费且可靠
- **阿里云OSS**: 国内访问速度快

### 性能优化
1. 启用Gzip压缩
2. 设置适当的缓存头
3. 使用CDN加速
4. 图片格式优化（WebP）

## 📞 技术支持

如有任何问题或建议，请通过以下方式联系：

- **邮箱**: <EMAIL>
- **GitHub**: 提交Issue或Pull Request
- **微信**: 德州Solver官方客服

## 📄 许可证

本项目采用 MIT 许可证，详情请查看 LICENSE 文件。

---

**德州Solver** - 让每一个决策都基于数学最优解 🃏
