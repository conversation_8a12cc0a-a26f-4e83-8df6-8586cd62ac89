/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Apple-inspired Color Palette */
    --primary-color: #007AFF;
    --primary-dark: #0056CC;
    --secondary-color: #5856D6;
    --accent-color: #FF3B30;
    --success-color: #34C759;
    --warning-color: #FF9500;

    /* Neutral Colors */
    --text-primary: #1D1D1F;
    --text-secondary: #86868B;
    --text-tertiary: #C7C7CC;
    --background-primary: #FFFFFF;
    --background-secondary: #F2F2F7;
    --background-tertiary: #FAFAFA;
    --border-color: #E5E5EA;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, #007AFF 0%, #5856D6 100%);
    --gradient-secondary: linear-gradient(135deg, #FF3B30 0%, #FF9500 100%);
    --gradient-hero: linear-gradient(135deg, #007AFF 0%, #5856D6 50%, #FF3B30 100%);

    /* Typography */
    --font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', <PERSON><PERSON>, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    --font-size-6xl: 3.75rem;

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
    --spacing-4xl: 6rem;

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

    /* Transitions */
    --transition-fast: 0.15s ease-out;
    --transition-normal: 0.3s ease-out;
    --transition-slow: 0.5s ease-out;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--background-primary);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: var(--spacing-md);
}

h1 { font-size: var(--font-size-5xl); }
h2 { font-size: var(--font-size-4xl); }
h3 { font-size: var(--font-size-3xl); }
h4 { font-size: var(--font-size-2xl); }
h5 { font-size: var(--font-size-xl); }
h6 { font-size: var(--font-size-lg); }

p {
    margin-bottom: var(--spacing-md);
    color: var(--text-secondary);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: var(--font-size-base);
    font-weight: 500;
    text-decoration: none;
    border-radius: var(--radius-xl);
    transition: all var(--transition-normal);
    cursor: pointer;
    border: none;
    white-space: nowrap;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: var(--background-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--background-primary);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-1px);
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-color);
    z-index: 1000;
    transition: all var(--transition-normal);
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md) var(--spacing-lg);
    max-width: 1200px;
    margin: 0 auto;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
    text-decoration: none;
}

.logo-icon {
    font-size: var(--font-size-2xl);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: var(--spacing-xl);
    align-items: center;
}

.nav-menu a {
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 500;
    transition: color var(--transition-fast);
    position: relative;
}

.nav-menu a:hover {
    color: var(--primary-color);
}

.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: width var(--transition-normal);
}

.nav-menu a:hover::after {
    width: 100%;
}

.nav-cta {
    background: var(--gradient-primary);
    color: white !important;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-lg);
    transition: all var(--transition-normal);
}

.nav-cta:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.nav-cta::after {
    display: none;
}

/* Hamburger Menu */
.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    transition: all var(--transition-normal);
    border-radius: 2px;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding-top: 80px;
}

.hero-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-4xl);
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
    position: relative;
    z-index: 2;
}

.hero-content {
    max-width: 600px;
}

.hero-title {
    font-size: var(--font-size-6xl);
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: var(--spacing-lg);
}

.title-line {
    display: block;
}

.gradient-text {
    background: var(--gradient-hero);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: var(--font-size-xl);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-2xl);
    line-height: 1.6;
}

.hero-buttons {
    display: flex;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-3xl);
}

.hero-stats {
    display: flex;
    gap: var(--spacing-2xl);
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--primary-color);
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Hero Visual */
.hero-visual {
    position: relative;
}

.poker-interface {
    background: white;
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-2xl);
    overflow: hidden;
    transform: perspective(1000px) rotateY(-5deg) rotateX(5deg);
    transition: transform var(--transition-slow);
}

.poker-interface:hover {
    transform: perspective(1000px) rotateY(0deg) rotateX(0deg);
}

.interface-header {
    background: var(--background-secondary);
    padding: var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
}

.header-controls {
    display: flex;
    gap: var(--spacing-sm);
}

.control {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.control.red { background: #FF5F57; }
.control.yellow { background: #FFBD2E; }
.control.green { background: #28CA42; }

.header-title {
    font-weight: 600;
    color: var(--text-primary);
}

.interface-content {
    padding: var(--spacing-xl);
}

.poker-table {
    margin-bottom: var(--spacing-xl);
}

.community-cards {
    display: flex;
    gap: var(--spacing-sm);
    justify-content: center;
    margin-bottom: var(--spacing-lg);
}

.card {
    width: 40px;
    height: 56px;
    background: white;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: var(--font-size-sm);
    box-shadow: var(--shadow-sm);
}

.player-position {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    justify-content: center;
}

.player-cards {
    display: flex;
    gap: var(--spacing-xs);
}

.player-info {
    text-align: center;
}

.player-name {
    display: block;
    font-weight: 600;
    font-size: var(--font-size-sm);
}

.player-stack {
    display: block;
    color: var(--text-secondary);
    font-size: var(--font-size-xs);
}

.gto-analysis {
    background: var(--background-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
}

.analysis-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.analysis-header h3 {
    font-size: var(--font-size-lg);
    margin: 0;
}

.confidence {
    font-size: var(--font-size-sm);
    color: var(--success-color);
    font-weight: 600;
}

.strategy-chart {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.strategy-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.action {
    min-width: 60px;
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.frequency-bar {
    flex: 1;
    height: 8px;
    background: var(--background-primary);
    border-radius: var(--radius-sm);
    overflow: hidden;
}

.frequency-fill {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: var(--radius-sm);
    transition: width var(--transition-normal);
}

.percentage {
    min-width: 40px;
    text-align: right;
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--primary-color);
}

/* Floating Cards Animation */
.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
    z-index: 1;
}

.floating-card {
    position: absolute;
    width: 60px;
    height: 84px;
    background: white;
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
    font-weight: 600;
    box-shadow: var(--shadow-lg);
    opacity: 0.1;
    animation: float 6s ease-in-out infinite;
}

.card-1 {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.card-2 {
    top: 60%;
    right: 15%;
    animation-delay: 1.5s;
}

.card-3 {
    top: 30%;
    right: 30%;
    animation-delay: 3s;
}

.card-4 {
    bottom: 20%;
    left: 20%;
    animation-delay: 4.5s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-20px) rotate(5deg);
    }
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: var(--spacing-4xl);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.section-header h2 {
    margin-bottom: var(--spacing-md);
}

.section-header p {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
}

/* Features Section */
.features {
    padding: var(--spacing-4xl) 0;
    background: var(--background-primary);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-2xl);
}

.feature-card {
    background: white;
    padding: var(--spacing-2xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
}

.feature-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-lg);
}

.feature-icon i {
    font-size: var(--font-size-xl);
    color: white;
}

.feature-card h3 {
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.feature-card p {
    margin-bottom: var(--spacing-lg);
}

.feature-highlight {
    display: inline-block;
    background: var(--background-secondary);
    color: var(--primary-color);
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-sm);
    font-weight: 600;
}

/* Demo Section */
.demo {
    padding: var(--spacing-4xl) 0;
    background: var(--background-secondary);
}

.demo-container {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: var(--spacing-4xl);
    align-items: center;
}

.demo-steps {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.step {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    transition: all var(--transition-normal);
    cursor: pointer;
}

.step.active {
    background: white;
    box-shadow: var(--shadow-md);
}

.step-number {
    width: 40px;
    height: 40px;
    background: var(--gradient-primary);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    flex-shrink: 0;
}

.step:not(.active) .step-number {
    background: var(--background-tertiary);
    color: var(--text-secondary);
}

.step-content h3 {
    margin-bottom: var(--spacing-xs);
    font-size: var(--font-size-lg);
}

.step-content p {
    margin: 0;
    font-size: var(--font-size-sm);
}

.demo-visual {
    position: relative;
    background: white;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
    min-height: 400px;
}

.demo-screen {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    padding: var(--spacing-2xl);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transform: translateX(20px);
    transition: all var(--transition-normal);
}

.demo-screen.active {
    opacity: 1;
    transform: translateX(0);
}

.input-demo {
    text-align: center;
}

.demo-cards {
    display: flex;
    gap: var(--spacing-sm);
    justify-content: center;
    margin-bottom: var(--spacing-lg);
}

.demo-card {
    width: 50px;
    height: 70px;
    background: white;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    box-shadow: var(--shadow-sm);
}

.demo-board {
    display: flex;
    gap: var(--spacing-xs);
    justify-content: center;
    margin-bottom: var(--spacing-lg);
}

.demo-position {
    font-weight: 600;
    color: var(--primary-color);
}

.action-demo {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    align-items: center;
}

.action-buttons {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    width: 100%;
    max-width: 200px;
}

.action-btn {
    padding: var(--spacing-md);
    border: 2px solid var(--border-color);
    background: white;
    border-radius: var(--radius-lg);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-normal);
}

.action-btn.selected {
    border-color: var(--primary-color);
    background: var(--primary-color);
    color: white;
}

.result-demo {
    width: 100%;
    max-width: 300px;
}

.result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
}

.result-score {
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-sm);
    font-weight: 600;
}

.result-score.good {
    background: var(--success-color);
    color: white;
}

.result-chart {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.result-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.result-bar {
    flex: 1;
    height: 12px;
    background: var(--background-secondary);
    border-radius: var(--radius-sm);
    overflow: hidden;
}

.result-fill {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: var(--radius-sm);
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .hamburger {
        display: flex;
    }

    .hero-container {
        grid-template-columns: 1fr;
        gap: var(--spacing-2xl);
        text-align: center;
    }

    .hero-title {
        font-size: var(--font-size-4xl);
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .hero-stats {
        justify-content: center;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .demo-container {
        grid-template-columns: 1fr;
    }

    .demo-steps {
        order: 2;
    }

    .demo-visual {
        order: 1;
    }
}

/* Technology Section */
.technology {
    padding: var(--spacing-4xl) 0;
    background: var(--background-primary);
}

.tech-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-2xl);
}

.tech-item {
    text-align: center;
    padding: var(--spacing-2xl);
    background: white;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.tech-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-secondary);
}

.tech-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.tech-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-secondary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-lg);
}

.tech-icon i {
    font-size: var(--font-size-2xl);
    color: white;
}

.tech-item h3 {
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.tech-item p {
    color: var(--text-secondary);
    margin: 0;
}

/* Testimonials Section */
.testimonials {
    padding: var(--spacing-4xl) 0;
    background: var(--background-secondary);
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-2xl);
}

.testimonial-card {
    background: white;
    padding: var(--spacing-2xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    position: relative;
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.testimonial-content {
    margin-bottom: var(--spacing-xl);
}

.testimonial-content p {
    font-style: italic;
    font-size: var(--font-size-lg);
    line-height: 1.6;
    color: var(--text-primary);
    margin: 0;
    position: relative;
}

.testimonial-content p::before {
    content: '"';
    font-size: var(--font-size-4xl);
    color: var(--primary-color);
    position: absolute;
    top: -10px;
    left: -20px;
    font-family: serif;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.author-avatar {
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--font-size-lg);
}

.author-info h4 {
    margin: 0;
    font-size: var(--font-size-base);
    color: var(--text-primary);
}

.author-info span {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.testimonial-rating {
    display: flex;
    gap: var(--spacing-xs);
}

.testimonial-rating i {
    color: var(--warning-color);
}

/* Pricing Section */
.pricing {
    padding: var(--spacing-4xl) 0;
    background: var(--background-primary);
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-2xl);
    max-width: 1000px;
    margin: 0 auto;
}

.pricing-card {
    background: white;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: all var(--transition-normal);
    position: relative;
}

.pricing-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.pricing-card.featured {
    transform: scale(1.05);
    border: 2px solid var(--primary-color);
}

.pricing-card.featured:hover {
    transform: scale(1.05) translateY(-8px);
}

.pricing-badge {
    position: absolute;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    background: var(--gradient-primary);
    color: white;
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-sm);
    font-weight: 600;
}

.pricing-header {
    padding: var(--spacing-2xl) var(--spacing-xl) var(--spacing-lg);
    text-align: center;
    border-bottom: 1px solid var(--border-color);
}

.pricing-header h3 {
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.price {
    display: flex;
    align-items: baseline;
    justify-content: center;
    gap: var(--spacing-xs);
}

.currency {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
}

.amount {
    font-size: var(--font-size-5xl);
    font-weight: 700;
    color: var(--text-primary);
}

.period {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
}

.pricing-features {
    padding: var(--spacing-xl);
}

.pricing-features ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.pricing-features li {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-sm) 0;
    color: var(--text-primary);
}

.pricing-features i.fa-check {
    color: var(--success-color);
}

.pricing-features i.fa-times {
    color: var(--text-tertiary);
}

.pricing-action {
    padding: var(--spacing-xl);
    text-align: center;
}

.pricing-action .btn {
    width: 100%;
    justify-content: center;
}

/* Download Section */
.download {
    padding: var(--spacing-4xl) 0;
    background: var(--gradient-primary);
    color: white;
}

.download-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-4xl);
    align-items: center;
}

.download-text h2 {
    color: white;
    margin-bottom: var(--spacing-lg);
}

.download-text p {
    color: rgba(255, 255, 255, 0.8);
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-xl);
}

.download-features {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.download-feature {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    color: rgba(255, 255, 255, 0.9);
}

.download-feature i {
    width: 20px;
    text-align: center;
}

.download-buttons {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.download-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg) var(--spacing-xl);
    background: white;
    color: var(--text-primary);
    text-decoration: none;
    border-radius: var(--radius-xl);
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-md);
}

.download-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.download-btn.primary {
    background: var(--text-primary);
    color: white;
}

.download-btn i {
    font-size: var(--font-size-2xl);
}

.btn-label {
    display: block;
    font-weight: 600;
    font-size: var(--font-size-base);
}

.btn-sublabel {
    display: block;
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.download-btn.primary .btn-sublabel {
    color: rgba(255, 255, 255, 0.7);
}

/* Footer */
.footer {
    background: var(--text-primary);
    color: white;
    padding: var(--spacing-4xl) 0 var(--spacing-xl);
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: var(--spacing-2xl);
    margin-bottom: var(--spacing-2xl);
}

.footer-section h4 {
    margin-bottom: var(--spacing-lg);
    color: white;
}

.footer-section ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.footer-section li {
    margin-bottom: var(--spacing-sm);
}

.footer-section a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.footer-section a:hover {
    color: white;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-lg);
}

.footer-section p {
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: var(--spacing-lg);
}

.social-links {
    display: flex;
    gap: var(--spacing-md);
}

.social-links a {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-normal);
}

.social-links a:hover {
    background: var(--primary-color);
    transform: translateY(-2px);
}

.footer-bottom {
    text-align: center;
    padding-top: var(--spacing-xl);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-bottom p {
    color: rgba(255, 255, 255, 0.5);
    margin: 0;
}

/* Additional Responsive Styles */
@media (max-width: 1024px) {
    .download-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .footer-content {
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-xl);
    }
}

@media (max-width: 768px) {
    .pricing-grid {
        grid-template-columns: 1fr;
    }

    .pricing-card.featured {
        transform: none;
    }

    .pricing-card.featured:hover {
        transform: translateY(-8px);
    }

    .testimonials-grid {
        grid-template-columns: 1fr;
    }

    .tech-grid {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .download-buttons {
        max-width: 300px;
        margin: 0 auto;
    }
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Loading Animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Scroll Animations */
.scroll-reveal {
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.6s ease-out;
}

.scroll-reveal.revealed {
    opacity: 1;
    transform: translateY(0);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--background-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-dark);
}

/* Focus Styles */
*:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.3);
}

/* Print Styles */
@media print {
    .navbar,
    .hero-background,
    .download,
    .footer {
        display: none;
    }

    body {
        font-size: 12pt;
        line-height: 1.4;
    }

    .container {
        max-width: none;
        padding: 0;
    }
}
